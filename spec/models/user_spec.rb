require 'rails_helper'

RSpec.describe User, type: :model do
  describe 'associations' do
    it { is_expected.to have_many(:food_events).with_foreign_key('cooker_id').dependent(:nullify) }
    it { is_expected.to have_many(:personas).with_foreign_key('alias_for_id').dependent(:nullify) }
  end

  describe 'enums' do
    it 'defines roles with correct values' do
      expect(described_class.roles).to eq({ 'regular' => 0, 'contributor' => 1, 'admin' => 2 })
    end
  end

  describe '#contributor_or_admin?' do
    let(:user) { described_class.new(role:) }

    context 'when the user is a contributor' do
      let(:role) { 'contributor' }

      it 'returns true' do
        expect(user.contributor_or_admin?).to be true
      end
    end

    context 'when the user is an admin' do
      let(:role) { 'admin' }

      it 'returns true' do
        expect(user.contributor_or_admin?).to be true
      end
    end

    context 'when the user is regular' do
      let(:role) { 'regular' }

      it 'returns false' do
        expect(user.contributor_or_admin?).to be false
      end
    end
  end

  describe 'deletion' do
    let(:user) { create(:user) }

    context 'when user has associated food_events' do
      before do
        create(:food_event, cooker: user)
      end

      it 'nullifies the cooker_id in food_events when user is deleted' do
        expect { user.destroy }.to change { FoodEvent.where(cooker_id: user.id).count }.from(1).to(0)
      end
    end

    context 'when user has associated personas' do
      before do
        create(:persona, alias_for: user, persona_type: nil, relationship: nil)
      end

      it 'nullifies the alias_for_id in personas when user is deleted' do
        expect { user.destroy }.to change { Persona.where(alias_for_id: user.id).count }.from(1).to(0)
      end
    end
  end
end

<% task_category = model %>

<%= render 'shared/model_form', model: task_category do |form| %>
  <div class="my-5">
    <%= form.label :title %>
    <%= form.text_field :title, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:parent_id, @available_task_categories, :id, :title, { prompt: "Does this category have a parent?" }, { class: "flex-1" }) %>
    <%= render 'shared/add_button', path: new_task_category_path if current_user&.contributor_or_admin? %>
  </div>
<% end %>

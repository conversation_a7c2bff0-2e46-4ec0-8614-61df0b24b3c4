<div class="grid landscape:grid-cols-2">
  <div class="flex items-baseline flex-wrap">
    <div class="w-1/2 aspect-square p-4">
      <%= link_to(
        eggs_path,
        class: 'w-full h-full border-2 rounded-2xl flex flex-col items-center justify-center'
      ) do %>
        <h2><%= @eggs_count %></h2>
        <h6>eggs in total</h6>
      <% end %>
    </div>

    <div class="w-1/2 aspect-square p-4">
      <%= link_to(
        sell_events_path,
        class: 'w-full h-full border-2 rounded-2xl flex flex-col items-center justify-center'
      ) do %>
        <h2>€<%= @sold_price %></h2>
        <h6>sold in total</h6>
      <% end %>
    </div>

    <div class="w-1/2 aspect-square p-4">
      <%= link_to(
        food_events_path,
        class: 'w-full h-full border-2 rounded-2xl flex flex-col items-center justify-center'
        ) do %>
        <h2><%= @meals %></h2>
        <h6>Meals</h6>
      <% end %>
    </div>

    <div class="w-1/2 aspect-square p-4">
      <%= link_to(
        weight_records_path,
        class: 'w-full h-full border-2 rounded-2xl flex flex-col items-center justify-center'
        ) do %>
        <h2><%= @meals %></h2>
        <h6>Weight Samples</h6>
      <% end %>
    </div>
  </div>

  <div class="divide-y divide-gray-400">
    <%= render 'layouts/admin_collapsible_section', title: 'User Accounts' do %>
      <table class="col-span-full">
        <thead class="h-16">
          <tr class="[&>th]:text-start">
            <th>Email</th>
            <th>Role</th>
            <th>Actions</th>
          </tr>
        </thead>

        <tbody>
          <% @users.each do |user| %>
            <tr class="h-8">
              <td><%= user.email %></td>
              <td>
                <%= form_with model: user, url: user_path(user), method: :patch, local: true, class: 'inline' do |form| %>
                  <%= form.select :role,
                                  options_for_select(User.roles.map { |key, value| [key.humanize, key] }, user.role),
                                  {},
                                  {
                                    class: 'border rounded px-2 py-1',
                                    onchange: 'this.form.submit();'
                                  } %>
                <% end %>
              </td>
              <td class="flex gap-2">
                <% unless user == current_user %>
                  <%= render 'shared/delete_button', target: user_path(user) %>
                <% end %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    <% end %>

    <%= render 'layouts/admin_collapsible_section', title: 'Farm Management' do %>
      <%= render 'layouts/admin_nav_item', target: chickens_path,           icon: 'chicken-icon.svg',           label: 'Chicken' %>
      <%= render 'layouts/admin_nav_item', target: eggs_path,               icon: 'eggs-icon.svg',              label: 'Eggs' %>
      <%= render 'layouts/admin_nav_item', target: weight_records_path,     icon: 'scale-icon.svg',             label: 'Weight' %>
      <%= render 'layouts/admin_nav_item', target: broodiness_periods_path, icon: 'broodiness-period-icon.svg', label: 'Broody Periods' %>
      <%= render 'layouts/admin_nav_item', target: task_categories_path,    icon: 'task-category-icon.svg',     label: 'Task Categories' %>
    <% end %>

    <%= render 'layouts/admin_collapsible_section', title: 'Food & Meals' do %>
      <%= render 'layouts/admin_nav_item', target: food_events_path, icon: 'meal-icon.svg',       label: 'Meals' %>
      <%= render 'layouts/admin_nav_item', target: food_types_path,  icon: 'food-types-icon.svg', label: 'Food Types' %>
    <% end %>

    <%= render 'layouts/admin_collapsible_section', title: 'Transactions', open: true do %>
      <%= render 'layouts/admin_nav_item', target: gift_events_path,        icon: 'gift-icon.svg',               label: 'Gifts' %>
      <%= render 'layouts/admin_nav_item', target: sell_events_path,        icon: 'sell-icon.svg',               label: 'Sells' %>
      <%= render 'layouts/admin_nav_item', target: purchase_events_path,    icon: 'purchase-event-icon.svg',     label: 'Purchases' %>
      <%= render 'layouts/admin_nav_item', target: products_path,           icon: 'product-icon.svg',            label: 'Products' %>
      <%= render 'layouts/admin_nav_item', target: product_categories_path, icon: 'product-categories-icon.svg', label: 'Product Categories' %>
    <% end %>

    <%= render 'layouts/admin_collapsible_section', title: 'Veterinary' do %>
      <%= render 'layouts/admin_nav_item', target: vet_visits_path,  icon: 'vet-visit-icon.svg',  label: 'Vet Visits' %>
      <%= render 'layouts/admin_nav_item', target: vet_clinics_path, icon: 'vet-clinic-icon.svg', label: 'Vet Clinics' %>
    <% end %>

    <%= render 'layouts/admin_collapsible_section', title: 'Transportation' do %>
      <%= render 'layouts/admin_nav_item', target: refuel_events_path,   icon: 'gas-station-1-icon.svg', label: 'Refuels' %>
      <%= render 'layouts/admin_nav_item', target: refuel_stations_path, icon: 'gas-station-2-icon.svg', label: 'Gas Stations' %>
    <% end %>

    <%= render 'layouts/admin_collapsible_section', title: 'People & Relationships' do %>
      <%= render 'layouts/admin_nav_item', target: personas_path,       icon: 'persona-icon.svg',       label: 'Personas' %>
      <%= render 'layouts/admin_nav_item', target: persona_types_path,  icon: 'persona-type-icon.svg',  label: 'Persona Types' %>
      <%= render 'layouts/admin_nav_item', target: relationships_path,  icon: 'relationship-icon.svg',  label: 'Relationships' %>
      <%= render 'layouts/admin_nav_item', target: documents_path,      icon: 'documents-icon.svg',     label: 'Documents' %>
      <%= render 'layouts/admin_nav_item', target: document_types_path, icon: 'documents-icon.svg',     label: 'Document Types' %>
    <% end %>

    <%= render 'layouts/admin_collapsible_section', title: 'Geography' do %>
      <%= render 'layouts/admin_nav_item', target: cities_path,      icon: 'city-icon.svg',  label: 'Cities' %>
      <%= render 'layouts/admin_nav_item', target: countries_path,   icon: 'iran-icon.svg',  label: 'Countries' %>
      <%= render 'layouts/admin_nav_item', target: continents_path,  icon: 'globe-icon.svg', label: 'Continents' %>
    <% end %>

    <%= render 'layouts/admin_collapsible_section', title: 'Units & Measurements' do %>
      <%= render 'layouts/admin_nav_item', target: units_path,           icon: 'currencies-icon.svg',    label: 'Units' %>
      <%= render 'layouts/admin_nav_item', target: unit_categories_path, icon: 'unit-category-icon.svg', label: 'Unit Categories' %>
    <% end %>
  </div>
</div>

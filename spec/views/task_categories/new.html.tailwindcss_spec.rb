require 'rails_helper'

RSpec.describe "task_categories/new", type: :view do
  before(:each) do
    assign(:task_category, create(:task_category))
  end

  it "renders new task_category form" do
    render

    assert_select "form[action=?][method=?]", task_categories_path, "post" do
      assert_select "input[name=?]", "task_category[title]"
      assert_select "input[name=?]", "task_category[parent_id]"
    end
  end
end

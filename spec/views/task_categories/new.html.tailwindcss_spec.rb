require 'rails_helper'

RSpec.describe "task_categories/new", type: :view do
  before(:each) do
    # Use a new (unsaved) task category for the new form
    assign(:task_category, build(:task_category))
    assign(:available_task_categories, TaskCategory.all.paginate(page: 1, per_page: 12))

    # Set current user for proper rendering
    allow(view).to receive(:current_user).and_return(create(:user, role: :contributor))
  end

  it "renders new task_category form" do
    render

    # Check that the page has the correct heading
    assert_select "h1", text: "New TaskCategory"

    # Check the form structure and fields
    assert_select "form[action=?][method=?]", task_categories_path, "post" do
      assert_select "input[name=?]", "task_category[title]"
      assert_select "select[name=?]", "task_category[parent_id]"
      assert_select "button[type=?]", "submit"
    end

    # Check that the form has the expected labels
    assert_select "label[for=?]", "task_category_title", text: "Title"
  end
end

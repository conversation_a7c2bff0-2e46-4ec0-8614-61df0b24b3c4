require 'rails_helper'

RSpec.describe UsersController, type: :controller do
  let(:admin_user) { create(:user, :admin, email: '<EMAIL>') }
  let(:regular_user) { create(:user, role: :regular, email: '<EMAIL>') }
  let(:target_user) { create(:user, role: :contributor, email: '<EMAIL>') }

  describe 'authorization' do
    context 'when user is not admin' do
      before { sign_in regular_user }

      it 'redirects unauthorized users for update action' do
        patch :update, params: { id: target_user.id, user: { role: 'admin' } }
        expect(response).to redirect_to(root_path)
      end

      it 'redirects unauthorized users for destroy action' do
        delete :destroy, params: { id: target_user.id }
        expect(response).to redirect_to(root_path)
      end
    end
  end

  context 'when user is admin' do
    before do
      sign_in admin_user
    end

    describe 'PATCH #update' do
      context 'when admin updates another user role' do
        it 'updates the user role successfully' do
          patch :update, params: { id: target_user.id, user: { role: 'admin' } }

          expect(response).to redirect_to(admin_dashboard_path)
          expect(flash[:notice]).to eq('User role was successfully updated.')
          expect(target_user.reload.role).to eq('admin')
        end
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(User).to receive(:update).and_return(false)
        end

        it 'redirects with error message' do
          patch :update, params: { id: target_user.id, user: { role: 'admin' } }

          expect(response).to redirect_to(admin_dashboard_path)
          expect(flash[:alert]).to eq('Failed to update user role.')
        end
      end
    end

    describe 'DELETE #destroy' do
      context 'when admin deletes another user' do
        it 'deletes the user successfully' do
          delete :destroy, params: { id: target_user.id }

          expect(response).to redirect_to(admin_dashboard_path)
          expect(flash[:notice]).to eq('User was successfully deleted.')
          expect { target_user.reload }.to raise_error(ActiveRecord::RecordNotFound)
        end
      end

      context 'when admin tries to delete themselves' do
        it 'prevents self-deletion' do
          delete :destroy, params: { id: admin_user.id }

          expect(response).to redirect_to(admin_dashboard_path)
          expect(flash[:alert]).to eq('You cannot delete yourself.')
          expect(admin_user.reload).to be_present
        end
      end
    end
  end
end

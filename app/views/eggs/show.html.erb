<%
  siblings_count = @egg.siblings.count

  sibling_eggs_title = if siblings_count.zero?
            'No other eggs were laid on the same day.'
          else
            "Eggs laid on the same day"
          end
%>

<%= render 'shared/show_page', model: @egg do %>
  <h3 class="heading-section pt-8 flex justify-between">
    <%= sibling_eggs_title %>
    <% unless siblings_count.zero? %>
      <span><%= siblings_count + 1 %></span>
    <% end %>
  </h3>

  <% if @egg.siblings.any? %>
    <ul class="space-y-2">
      <% (@egg.siblings.or(Egg.where(id: @egg.id))).each do |egg| %>
        <li>
          <%= link_to egg,  class: "border p-4 rounded-md flex gap-4" do %>
            <% if egg.data_missing? %>
              <%= image_tag 'exclamation-triangle-icon.svg', class: 'h-8', loading: 'lazy' %>
            <% else %>
              <%= image_tag 'checkmark-circled-icon.svg', class: 'h-8', loading: 'lazy' %>
            <% end %>
            <span class='font-semibold'><%= egg.id %></span>
            <span><%= egg.chickens.order(:name).pluck(:name).join(', ') %></span>
          <% end %>
        </li>
      <% end %>
    </ul>
  <% else %>
    <%= image_tag 'broken-heart.svg', class: 'w-1/3 lg:w-48 m-auto', loading: 'lazy' %>
  <% end %>
<% end %>

require 'rails_helper'

RSpec.describe "task_categories/index", type: :view do
  before(:each) do
    create(:task_category, title: "Task 1")
    create(:task_category, title: "Task 2")
    assign(:task_categories, TaskCategory.all.then(&apply_pagination(per_page: 12)))
  end

  it "renders a list of task_categories" do
    render
    cell_selector = 'ul > li'
    assert_select cell_selector, text: Regexp.new("Task 1".to_s), count: 1
    assert_select cell_selector, text: Regexp.new("Task 2".to_s), count: 1
  end
end

require 'rails_helper'

RSpec.describe "task_categories/index", type: :view do
  before(:each) do
    create(:task_category, title: "Task 1")
    create(:task_category, title: "Task 2")

    # Create a paginated collection using will_paginate's paginate method
    assign(:task_categories, TaskCategory.all.paginate(page: 1, per_page: 12))

    # Set the action name so the partial renders correctly
    controller.action_name = 'index'

    # Set current user for proper rendering of edit buttons
    allow(view).to receive(:current_user).and_return(create(:user, role: :contributor))
  end

  it "renders a list of task_categories" do
    render
    # Check that we have the correct number of list items
    assert_select 'ul > li', count: 2

    # Check that the titles are rendered in h4 elements within the list items
    assert_select 'ul > li h4', text: "Task 1", count: 1
    assert_select 'ul > li h4', text: "Task 2", count: 1

    # Check that edit buttons are rendered for contributors
    assert_select 'ul > li a[href*="/edit"]', count: 2
  end
end

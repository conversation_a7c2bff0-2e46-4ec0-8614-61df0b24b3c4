# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.2].define(version: 2025_07_27_155331) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "broodiness_periods", force: :cascade do |t|
    t.bigint "chicken_id", null: false
    t.datetime "start_day"
    t.datetime "end_day"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["chicken_id"], name: "index_broodiness_periods_on_chicken_id"
  end

  create_table "chickens", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "birth_day"
    t.datetime "death_day"
    t.datetime "join_day"
    t.datetime "leave_day"
    t.bigint "gender_id", default: 1, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "color"
    t.index "lower((name)::text)", name: "index_chickens_on_lower_name", unique: true
    t.index ["gender_id"], name: "index_chickens_on_gender_id"
  end

  create_table "chickens_eggs", id: false, force: :cascade do |t|
    t.bigint "egg_id", null: false
    t.bigint "chicken_id", null: false
    t.index ["chicken_id", "egg_id"], name: "index_chickens_eggs_on_chicken_id_and_egg_id"
    t.index ["egg_id", "chicken_id"], name: "index_chickens_eggs_on_egg_id_and_chicken_id"
  end

  create_table "cities", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "country_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["country_id"], name: "index_cities_on_country_id"
  end

  create_table "continents", force: :cascade do |t|
    t.string "name", null: false
    t.string "code", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["code"], name: "index_continents_on_code", unique: true
    t.index ["name"], name: "index_continents_on_name", unique: true
  end

  create_table "countries", force: :cascade do |t|
    t.string "name", null: false
    t.string "code", null: false
    t.bigint "continent_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["code"], name: "index_countries_on_code", unique: true
    t.index ["continent_id"], name: "index_countries_on_continent_id"
  end

  create_table "currencies", force: :cascade do |t|
    t.string "name"
    t.string "code"
    t.string "sign"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "document_types", force: :cascade do |t|
    t.string "title", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["title"], name: "index_document_types_on_title", unique: true
  end

  create_table "documents", force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.date "issued_on"
    t.date "expires_on"
    t.bigint "issuer_id"
    t.bigint "document_type_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["document_type_id"], name: "index_documents_on_document_type_id"
    t.index ["issuer_id"], name: "index_documents_on_issuer_id"
  end

  create_table "documents_personas", id: false, force: :cascade do |t|
    t.bigint "document_id", null: false
    t.bigint "persona_id", null: false
    t.index ["document_id", "persona_id"], name: "index_documents_personas_on_document_id_and_persona_id"
    t.index ["persona_id", "document_id"], name: "index_documents_personas_on_persona_id_and_document_id"
  end

  create_table "eggs", force: :cascade do |t|
    t.datetime "laid_at", default: -> { "now()" }, null: false
    t.decimal "height"
    t.decimal "weight"
    t.decimal "width"
    t.decimal "content_health", default: "1.0"
    t.decimal "shape_health", default: "1.0"
    t.decimal "skin_health", default: "1.0"
    t.boolean "fertilized", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "food_event_id"
    t.bigint "gift_event_id"
    t.boolean "unknown_fate", default: false, null: false
    t.boolean "laid_at_time_unknown", default: false
    t.boolean "nader_defect", default: false, null: false
    t.boolean "juan_jose_defect", default: false, null: false
    t.boolean "shahbagha_defect", default: false, null: false
    t.bigint "sell_event_id"
    t.boolean "double_yolked", default: false, null: false
    t.boolean "mahlagha_defect", default: false, null: false
    t.index ["food_event_id"], name: "index_eggs_on_food_event_id"
    t.index ["gift_event_id"], name: "index_eggs_on_gift_event_id"
    t.index ["sell_event_id"], name: "index_eggs_on_sell_event_id"
    t.check_constraint "0.00 <= content_health AND content_health <= 1.00", name: "content_health_value_boundaries"
    t.check_constraint "0.00 <= shape_health AND shape_health <= 1.00", name: "shape_health_value_boundaries"
    t.check_constraint "0.00 <= skin_health AND skin_health <= 1.00", name: "skin_health_value_boundaries"
  end

  create_table "food_events", force: :cascade do |t|
    t.bigint "food_type_id", null: false
    t.datetime "date"
    t.bigint "cooker_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["cooker_id"], name: "index_food_events_on_cooker_id"
    t.index ["food_type_id"], name: "index_food_events_on_food_type_id"
  end

  create_table "food_types", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "genders", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_genders_on_name", unique: true
    t.check_constraint "name::text = ANY (ARRAY['female'::character varying, 'male'::character varying]::text[])", name: "valid_gender_values"
  end

  create_table "geo_locations", force: :cascade do |t|
    t.decimal "latitude", precision: 10, scale: 6
    t.decimal "longitude", precision: 10, scale: 6
    t.string "address"
    t.string "google_maps_uri"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["latitude", "longitude", "address"], name: "index_geo_locations_on_lat_lng_address", unique: true
  end

  create_table "gift_events", force: :cascade do |t|
    t.datetime "date"
    t.bigint "persona_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["persona_id"], name: "index_gift_events_on_persona_id"
  end

  create_table "image_associations", force: :cascade do |t|
    t.bigint "image_id", null: false
    t.string "imageable_type", null: false
    t.bigint "imageable_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["image_id"], name: "index_image_associations_on_image_id"
    t.index ["imageable_type", "imageable_id"], name: "index_image_associations_on_imageable"
  end

  create_table "images", force: :cascade do |t|
    t.string "label"
    t.string "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "persona_types", force: :cascade do |t|
    t.string "title", null: false
    t.bigint "parent_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["parent_id"], name: "index_persona_types_on_parent_id"
    t.index ["title"], name: "index_persona_types_on_title", unique: true
  end

  create_table "personas", force: :cascade do |t|
    t.string "nickname", null: false
    t.bigint "relationship_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "persona_type_id"
    t.bigint "alias_for_id"
    t.index ["alias_for_id"], name: "index_personas_on_alias_for_id"
    t.index ["nickname"], name: "index_personas_on_nickname", unique: true
    t.index ["persona_type_id"], name: "index_personas_on_persona_type_id"
    t.index ["relationship_id"], name: "index_personas_on_relationship_id"
  end

  create_table "product_categories", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "parent_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_product_categories_on_name", unique: true
    t.index ["parent_id"], name: "index_product_categories_on_parent_id"
  end

  create_table "products", force: :cascade do |t|
    t.string "name"
    t.bigint "product_category_id", null: false
    t.bigint "supplier_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_category_id"], name: "index_products_on_product_category_id"
    t.index ["supplier_id"], name: "index_products_on_supplier_id"
  end

  create_table "purchase_event_products", force: :cascade do |t|
    t.bigint "purchase_event_id", null: false
    t.bigint "product_id", null: false
    t.date "production_date"
    t.date "expiry_date"
    t.decimal "quantity"
    t.bigint "unit_id", null: false
    t.decimal "paid_price"
    t.decimal "original_price"
    t.bigint "currency_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["currency_id"], name: "index_purchase_event_products_on_currency_id"
    t.index ["product_id"], name: "index_purchase_event_products_on_product_id"
    t.index ["purchase_event_id"], name: "index_purchase_event_products_on_purchase_event_id"
    t.index ["unit_id"], name: "index_purchase_event_products_on_unit_id"
  end

  create_table "purchase_events", force: :cascade do |t|
    t.bigint "buyer_id", null: false
    t.bigint "seller_id", null: false
    t.datetime "purchased_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["buyer_id"], name: "index_purchase_events_on_buyer_id"
    t.index ["seller_id"], name: "index_purchase_events_on_seller_id"
  end

  create_table "refuel_events", force: :cascade do |t|
    t.bigint "refuel_station_id", null: false
    t.datetime "refuled_at", null: false
    t.decimal "amount", null: false
    t.decimal "paid_price", null: false
    t.bigint "currency_id", null: false
    t.bigint "unit_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "price_per_unit"
    t.index ["currency_id"], name: "index_refuel_events_on_currency_id"
    t.index ["refuel_station_id"], name: "index_refuel_events_on_refuel_station_id"
    t.index ["unit_id"], name: "index_refuel_events_on_unit_id"
  end

  create_table "refuel_stations", force: :cascade do |t|
    t.bigint "brand_id", null: false
    t.string "nickname"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "latitude", precision: 10, scale: 6
    t.decimal "longitude", precision: 10, scale: 6
    t.bigint "city_id"
    t.string "address"
    t.index ["brand_id"], name: "index_refuel_stations_on_brand_id"
    t.index ["city_id"], name: "index_refuel_stations_on_city_id"
    t.index ["nickname"], name: "index_refuel_stations_on_nickname", unique: true
  end

  create_table "relationships", force: :cascade do |t|
    t.string "title", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["title"], name: "index_relationships_on_title", unique: true
  end

  create_table "sell_events", force: :cascade do |t|
    t.datetime "date"
    t.bigint "persona_id"
    t.decimal "price"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["persona_id"], name: "index_sell_events_on_persona_id"
  end

  create_table "task_categories", force: :cascade do |t|
    t.string "title", null: false
    t.bigint "parent_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index "parent_id, lower((title)::text)", name: "index_task_categories_on_parent_and_title_case_insensitive", unique: true
    t.index ["parent_id"], name: "index_task_categories_on_parent_id"
    t.check_constraint "title::text <> ''::text", name: "task_categories_title_not_empty"
  end

  create_table "unit_categories", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.bigint "parent_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_unit_categories_on_name", unique: true
    t.index ["parent_id"], name: "index_unit_categories_on_parent_id"
  end

  create_table "units", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.string "sign"
    t.bigint "unit_category_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_units_on_name", unique: true
    t.index ["unit_category_id"], name: "index_units_on_unit_category_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "role", default: 0
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  create_table "vet_clinics", force: :cascade do |t|
    t.string "name", null: false
    t.bigint "city_id", null: false
    t.string "address", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["address", "city_id", "name"], name: "index_vet_clinics_on_address_and_city_id_and_name", unique: true
    t.index ["city_id"], name: "index_vet_clinics_on_city_id"
  end

  create_table "vet_visits", force: :cascade do |t|
    t.bigint "vet_id", null: false
    t.datetime "date", null: false
    t.bigint "vet_clinic_id"
    t.boolean "at_home", default: false, null: false
    t.bigint "chicken_id", null: false
    t.text "situation_description"
    t.text "doctor_verdict"
    t.text "prescription"
    t.boolean "injection_in_place"
    t.boolean "medicine_in_place"
    t.boolean "has_medicine_routine"
    t.boolean "needs_second_visit"
    t.integer "severity", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "previous_visit_id"
    t.index ["chicken_id"], name: "index_vet_visits_on_chicken_id"
    t.index ["previous_visit_id"], name: "index_vet_visits_on_previous_visit_id"
    t.index ["vet_clinic_id"], name: "index_vet_visits_on_vet_clinic_id"
    t.index ["vet_id"], name: "index_vet_visits_on_vet_id"
  end

  create_table "weight_records", force: :cascade do |t|
    t.string "entity_type", null: false
    t.bigint "entity_id", null: false
    t.datetime "date"
    t.integer "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["entity_type", "entity_id"], name: "index_weight_records_on_entity"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "broodiness_periods", "chickens"
  add_foreign_key "chickens", "genders"
  add_foreign_key "cities", "countries"
  add_foreign_key "countries", "continents"
  add_foreign_key "documents", "document_types"
  add_foreign_key "documents", "personas", column: "issuer_id"
  add_foreign_key "eggs", "food_events"
  add_foreign_key "eggs", "gift_events"
  add_foreign_key "eggs", "sell_events"
  add_foreign_key "food_events", "food_types"
  add_foreign_key "food_events", "users", column: "cooker_id"
  add_foreign_key "gift_events", "personas"
  add_foreign_key "image_associations", "images"
  add_foreign_key "persona_types", "personas", column: "parent_id"
  add_foreign_key "personas", "persona_types"
  add_foreign_key "personas", "relationships"
  add_foreign_key "personas", "users", column: "alias_for_id"
  add_foreign_key "product_categories", "product_categories", column: "parent_id"
  add_foreign_key "products", "personas", column: "supplier_id"
  add_foreign_key "products", "product_categories"
  add_foreign_key "purchase_event_products", "currencies"
  add_foreign_key "purchase_event_products", "products"
  add_foreign_key "purchase_event_products", "purchase_events"
  add_foreign_key "purchase_event_products", "units"
  add_foreign_key "purchase_events", "personas", column: "buyer_id"
  add_foreign_key "purchase_events", "personas", column: "seller_id"
  add_foreign_key "refuel_events", "currencies"
  add_foreign_key "refuel_events", "refuel_stations"
  add_foreign_key "refuel_events", "units"
  add_foreign_key "refuel_stations", "cities"
  add_foreign_key "refuel_stations", "personas", column: "brand_id"
  add_foreign_key "sell_events", "personas"
  add_foreign_key "task_categories", "task_categories", column: "parent_id"
  add_foreign_key "unit_categories", "unit_categories", column: "parent_id"
  add_foreign_key "units", "unit_categories"
  add_foreign_key "vet_clinics", "cities"
  add_foreign_key "vet_visits", "chickens"
  add_foreign_key "vet_visits", "personas", column: "vet_id"
  add_foreign_key "vet_visits", "vet_clinics"
  add_foreign_key "vet_visits", "vet_visits", column: "previous_visit_id"
end

class CreateTaskCategories < ActiveRecord::Migration[7.2]
  def change
    create_table :task_categories do |t|
      t.string :title, null: false
      t.references :parent, foreign_key: { to_table: :task_categories }

      t.timestamps
    end

    add_check_constraint :task_categories, "title != ''", name: 'task_categories_title_not_empty'
    execute <<-SQL
      CREATE UNIQUE INDEX index_task_categories_on_parent_and_title_case_insensitive
      ON task_categories (parent_id, lower(title));
    SQL
  end
end

class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable

  enum role: { regular: 0, contributor: 1, admin: 2 }

  # Associations
  has_many :food_events, foreign_key: 'cooker_id', dependent: :nullify
  has_many :personas, foreign_key: 'alias_for_id', dependent: :nullify

  def contributor_or_admin?
    contributor? || admin?
  end
end

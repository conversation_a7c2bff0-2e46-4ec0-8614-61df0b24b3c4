class UsersController < ApplicationController
  before_action :authenticate_user!
  before_action :set_user, only: %i[update destroy]

  # PATCH/PUT /users/1
  def update
    authorize @user

    respond_to do |format|
      if @user.update(user_params)
        format.html { redirect_to admin_dashboard_path, notice: 'User role was successfully updated.' }
        format.json { render json: @user, status: :ok }
      else
        format.html { redirect_to admin_dashboard_path, alert: 'Failed to update user role.' }
        format.json { render json: @user.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /users/1
  def destroy
    if @user == current_user
      respond_to do |format|
        format.html { redirect_to admin_dashboard_path, alert: 'You cannot delete yourself.' }
        format.json { render json: { error: 'Cannot delete yourself' }, status: :forbidden }
      end
      return
    end

    authorize @user

    @user.destroy!

    respond_to do |format|
      format.html { redirect_to admin_dashboard_path, notice: 'User was successfully deleted.' }
      format.json { head :no_content }
    end
  end

  private

  def set_user
    @user = User.find(params[:id])
  end

  def user_params
    params.require(:user).permit(:role)
  end
end

require 'rails_helper'

RSpec.describe TaskCategory, type: :model do
  subject { FactoryBot.build(:task_category) }

  describe 'associations' do
    it { is_expected.to belong_to(:parent).optional(true).class_name('TaskCategory') }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:title) }
    it { is_expected.to validate_uniqueness_of(:title).scoped_to(:parent_id).case_insensitive }
    it { is_expected.to validate_length_of(:title).is_at_least(1).is_at_most(255) }
  end
end
